import { useEffect } from 'react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { cn } from "@/src/background/util";
import { useConfig } from './ConfigContext';
import type { Config } from '../types/config';

interface WebSearchModeSelectorProps {
  webMode: Config['webMode'];
  updateConfig: (newConfig: Partial<Config>) => void;
}

const WebSearchModeSelector = ({ webMode, updateConfig }: WebSearchModeSelectorProps) => (
  <div className="space-y-3">
    <Label className="text-apple-body font-medium text-foreground">Search Provider</Label>
    <RadioGroup
      value={webMode}
      onValueChange={(value) => updateConfig({ webMode: value as Config['webMode'] })}
      className="space-y-2"
    >
      {['Google'].map(mode => (
        <div key={mode} className="flex items-center space-x-2">
          <RadioGroupItem
            value={mode}
            id={`webMode-${mode}`}
          />
          <Label
            htmlFor={`webMode-${mode}`}
            className="text-apple-body font-medium cursor-pointer"
          >
            {mode}
          </Label>
        </div>
      ))}
    </RadioGroup>
  </div>
);

interface SerpSettingsPanelProps {
  config: Config;
  updateConfig: (newConfig: Partial<Config>) => void;
}

const SerpSettingsPanel = ({ config, updateConfig }: SerpSettingsPanelProps) => {
  const charLimit = config?.webLimit ?? 16; 
  const maxLinks = config?.serpMaxLinksToVisit ?? 3;

  return (
    <div className="space-y-6">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label htmlFor="maxLinks" className="text-apple-body font-medium text-foreground">
            Max Links to Visit
          </Label>
          <span className="text-apple-footnote text-muted-foreground font-mono">
            {maxLinks}
          </span>
        </div>
        <Slider
          id="maxLinks"
          value={[maxLinks]}
          max={10}
          min={1}
          step={1}
          onValueChange={value => updateConfig({ serpMaxLinksToVisit: value[0] })}
          className="w-full"
        />
        <p className="text-apple-caption2 text-muted-foreground">
          Number of search result links to fetch and analyze.
        </p>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label htmlFor="charLimit" className="text-apple-body font-medium text-foreground">
            Content Character Limit
          </Label>
          <span className="text-apple-footnote text-muted-foreground font-mono">
            {charLimit === 128 ? 'Unlimited' : `${charLimit}k chars`}
          </span>
        </div>
        <Slider
          id="charLimit"
          value={[charLimit]}
          max={128} 
          min={1}   
          step={1}
          onValueChange={value => updateConfig({ webLimit: value[0] })}
          className="w-full"
        />
        <p className="text-apple-caption2 text-muted-foreground">
          Maximum characters (in thousands) of content to analyze per page. Set to 128k for unlimited.
        </p>
      </div>
    </div>
  );
};

export const WebSearchCard = () => {
  const { config, updateConfig } = useConfig();

  useEffect(() => {
    if (config?.webMode === 'Google') {
      const updates: Partial<Config> = {};
      if (typeof config?.serpMaxLinksToVisit === 'undefined') updates.serpMaxLinksToVisit = 3;
      if (typeof config?.webLimit === 'undefined') updates.webLimit = 16;
      if (Object.keys(updates).length > 0) updateConfig(updates);
    }
  }, [config?.webMode, config?.serpMaxLinksToVisit, config?.webLimit, updateConfig]);

  return (
    <div className={cn(
      "bg-card border border-border rounded-xl shadow-sm",
      "hover:shadow-md hover:border-border/80",
      "overflow-hidden"
    )}>
      <div className={cn(
        "px-6 py-4 border-b border-border/50"
      )}>
        <div>
          <h3 className="text-apple-title3 font-semibold text-foreground">Web Search</h3>
          <p className="text-apple-footnote text-muted-foreground">Configure search behavior and limits</p>
        </div>
      </div>

      <div className="p-6 space-y-6">
        <WebSearchModeSelector updateConfig={updateConfig} webMode={config?.webMode} />
        
        {config?.webMode === 'Google' && (
          <div className="pt-2 border-t border-border/50">
            <SerpSettingsPanel config={config} updateConfig={updateConfig} />
          </div>
        )}
        
        {!config?.webMode && (
          <div className="pt-2 border-t border-border/50">
            <p className="text-muted-foreground text-apple-body">
              Select a search provider to configure its settings.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
