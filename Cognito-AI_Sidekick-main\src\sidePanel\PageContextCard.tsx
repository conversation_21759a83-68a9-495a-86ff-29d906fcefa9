import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { cn } from "@/src/background/util";
import { useConfig } from './ConfigContext';

interface ContextLimitSliderProps {
  size: number;
  updateConfig: ReturnType<typeof useConfig>['updateConfig'];
}

const ContextLimitSlider = ({ size, updateConfig }: ContextLimitSliderProps) => (
  <div className="space-y-3">
    <div className="flex items-center justify-between">
      <Label htmlFor="contextLimit" className="text-apple-body font-medium text-foreground">
        Character Limit
      </Label>
      <span className="text-apple-footnote text-muted-foreground font-mono">
        {size === 128 ? 'Unlimited' : `${size}k chars`}
      </span>
    </div>
    <Slider
      id="contextLimit"
      value={[size]}
      max={128}
      min={1}
      step={1}
      onValueChange={(value: number[]) => updateConfig({ contextLimit: value[0] })}
      className="w-full"
    />
    <p className="text-apple-caption2 text-muted-foreground">
      Maximum amount of page content to include in context. Higher values provide more context but use more tokens.
    </p>
  </div>
);

export const PageContextCard = () => {
  const { config, updateConfig } = useConfig();
  const size = config?.contextLimit || 1;

  return (
    <div className={cn(
      "bg-card border border-border rounded-xl shadow-sm",
      "hover:shadow-md hover:border-border/80",
      "overflow-hidden"
    )}>
      <div className={cn(
        "px-6 py-4 border-b border-border/50"
      )}>
        <div>
          <h3 className="text-apple-title3 font-semibold text-foreground">Page Context</h3>
          <p className="text-apple-footnote text-muted-foreground">Control how much page content to analyze</p>
        </div>
      </div>

      <div className="p-6">
        <ContextLimitSlider size={size} updateConfig={updateConfig} />
      </div>
    </div>
  );
};
