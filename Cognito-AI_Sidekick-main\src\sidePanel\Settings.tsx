import { useConfig } from './ConfigContext';
import { ConnectCard } from './ConnectCard';
import { PageContextCard } from './PageContextCard';
import { ModelSettingsCard } from './ModelSettingsCard';
import { PersonaCard } from './PersonaCard';
import { WebSearchCard } from './WebSearchCard';
import { Button } from '@/components/ui/button';
import { cn } from 'src/background/util';
import { useState } from 'react';

export const Settings = () => {
  const { config } = useConfig();
  const [showWarning, setShowWarning] = useState(!config?.models || config.models.length === 0);

  return (
    <div
      id="settings"
      className="relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden"
      >
      <div className="flex justify-center mb-6">
        <div id="kofi-widget">
          <a href='https://ko-fi.com/T6T11G2CYS' target='_blank' rel="noopener noreferrer">
            <img
              height='36'
              style={{border: '0px', height: '36px'}}
              src='https://storage.ko-fi.com/cdn/kofi6.png?v=6'
              alt='Buy Me a Coffee at ko-fi.com'
            />
          </a>
        </div>
      </div>
      {showWarning && (
        <div className={cn(
          "mb-8",
          "bg-card border border-border rounded-xl shadow-sm",
          "hover:shadow-md hover:border-border/80",
          "overflow-hidden"
        )}>
          {/* Header */}
          <div className="px-6 py-5 border-b border-border/50 bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="text-center">
              <h2 className="text-apple-title2 font-semibold text-foreground mb-2">Welcome to Chromepanion</h2>
              <p className="text-apple-callout text-muted-foreground">Get started with Ollama in just a few steps</p>
            </div>
          </div>

          {/* Setup Steps */}
          <div className="p-6">
            <div className="space-y-6">
              {/* Step 1: Ollama Setup */}
              <div className="relative">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center text-apple-footnote font-semibold shadow-sm">
                    1
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-apple-headline font-semibold text-foreground mb-3">Install & Setup Ollama</h3>
                    <div className="space-y-3">
                      <div className="bg-muted/30 rounded-lg p-4 border border-border/30">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="w-2 h-2 rounded-full bg-primary/60"></span>
                            <span className="text-apple-callout text-foreground">Download from <code className="px-1.5 py-0.5 bg-muted rounded text-apple-footnote font-mono">ollama.com</code></span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="w-2 h-2 rounded-full bg-primary/60"></span>
                            <span className="text-apple-callout text-foreground">Service runs at <code className="px-1.5 py-0.5 bg-muted rounded text-apple-footnote font-mono">localhost:11434</code></span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="w-2 h-2 rounded-full bg-primary/60"></span>
                            <span className="text-apple-callout text-foreground">Pull model: <code className="px-1.5 py-0.5 bg-muted rounded text-apple-footnote font-mono">ollama pull llama3.2:3b</code></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step 2: Start Chatting */}
              <div className="relative">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center text-apple-footnote font-semibold shadow-sm">
                    2
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-apple-headline font-semibold text-foreground mb-2">Start Chatting</h3>
                    <p className="text-apple-callout text-muted-foreground">Close settings and use the persona selector to choose your model</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Button */}
            <div className="mt-8 flex justify-center">
              <Button
                variant="default"
                className="bg-primary text-white hover:bg-primary/90 rounded-xl px-8 py-3 text-apple-callout font-semibold shadow-sm hover:shadow-md transition-all duration-200"
                onClick={() => setShowWarning(false)}
              >
                Get Started
              </Button>
            </div>

            {/* Footer Note */}
            <div className="mt-6 text-center">
              <p className="text-apple-footnote text-muted-foreground/80">
                You can customize other settings anytime from the menu
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-6">
        <ConnectCard />
        <ModelSettingsCard />
        <PersonaCard />
        <PageContextCard />
        <WebSearchCard />
        <div className="pointer-events-none h-8" />
      </div>
    </div>
  );
};
