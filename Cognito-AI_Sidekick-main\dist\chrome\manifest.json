{"version": "1.0.0", "manifest_version": 3, "name": "chromepanion", "description": "ChromePanion uses AI to interpret your needs, letting you guide, query, and control your Chrome browser through natural interaction.", "permissions": ["activeTab", "storage", "sidePanel", "scripting", "tabs", "declarativeNetRequest"], "minimum_chrome_version": "114", "action": {"default_title": "Click to open panel"}, "side_panel": {"default_path": "sidePanel.html"}, "icons": {"16": "assets/images/chromepanion.png", "48": "assets/images/chromepanion.png", "128": "assets/images/chromepanion.png"}, "background": {"service_worker": "background.js"}, "web_accessible_resources": [{"resources": ["assets/**", "content.js.map"], "matches": ["<all_urls>"]}], "host_permissions": ["<all_urls>"], "content_security_policy": {"extension_pages": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; font-src 'self'; img-src 'self' data: http://localhost:* http://127.0.0.1:* https:; connect-src 'self' file: http: https: http://localhost:* http://127.0.0.1:* https://www.google.com https://google.com https://cdnjs.cloudflare.com;"}}