import React from 'react';
import { cn } from "@/src/background/util";
import { useConfig } from './ConfigContext';

interface WelcomeInterfaceProps {
  onSendMessage?: (message: string) => void;
}

interface SuggestedPrompt {
  title: string;
  description: string;
  prompt: string;
  persona: string;
  icon: string;
}

const suggestedPrompts: SuggestedPrompt[] = [
  {
    title: "Summarize this page",
    description: "Get a structured summary of the main points on this page",
    prompt: "Summarize the main points on this page",
    persona: "Scholar",
    icon: "🎓"
  },
  {
    title: "Monitor industry news",
    description: "Stay updated with the latest developments in your field",
    prompt: "Monitor industry news about cryptocurrency and blockchain technology",
    persona: "Executive", 
    icon: "💼"
  },
  {
    title: "Creative writing help",
    description: "Get assistance with creative and engaging content",
    prompt: "Write some funny Out of Office email responses to use while I'm on vacation from May 23-27",
    persona: "Storyteller",
    icon: "📚"
  },
  {
    title: "Fact-check information",
    description: "Verify claims and analyze information critically",
    prompt: "Fact-check recent claims about AI technology and its impact on jobs",
    persona: "Skeptic",
    icon: "🤔"
  }
];

export const WelcomeInterface: React.FC<WelcomeInterfaceProps> = ({ onSendMessage }) => {
  const { config, updateConfig } = useConfig();

  const handlePromptClick = (prompt: SuggestedPrompt) => {
    // Switch to the appropriate persona
    updateConfig({ persona: prompt.persona });
    
    // Send the message
    if (onSendMessage) {
      onSendMessage(prompt.prompt);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-full px-6 py-8 min-h-[400px]">
      {/* Welcome Header */}
      <div className="text-center mb-10 max-w-md">
        <h1 className="text-3xl font-semibold text-foreground mb-4 font-['SF_Pro_Display',_system-ui,_sans-serif] tracking-tight">
          Welcome to Chromepanion
        </h1>
        <p className="text-foreground/70 text-base leading-relaxed font-['SF_Pro_Text',_system-ui,_sans-serif]">
          Your private AI web search assistant. Choose a prompt below to get started, or type your own question.
        </p>
      </div>

      {/* Suggested Prompts Grid */}
      <div className="grid grid-cols-1 gap-4 w-full max-w-lg">
        {suggestedPrompts.map((prompt, index) => (
          <button
            key={index}
            onClick={() => handlePromptClick(prompt)}
            className={cn(
              "group relative p-5 text-left rounded-2xl border border-border/20 bg-background/50",
              "hover:border-border/40 hover:bg-background/80 hover:shadow-lg hover:shadow-black/5",
              "transition-all duration-300 ease-out hover:scale-[1.02]",
              "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30"
            )}
          >
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-10 h-10 rounded-xl bg-muted/50 flex items-center justify-center text-xl">
                {prompt.icon}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-foreground text-base mb-2 font-['SF_Pro_Text',_system-ui,_sans-serif]">
                  {prompt.title}
                </h3>
                <p className="text-foreground/60 text-sm leading-relaxed font-['SF_Pro_Text',_system-ui,_sans-serif]">
                  {prompt.description}
                </p>
              </div>
              <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg
                  className="w-5 h-5 text-foreground/40"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Footer Note */}
      <div className="mt-10 text-center">
        <p className="text-foreground/50 text-sm font-['SF_Pro_Text',_system-ui,_sans-serif]">
          All processing happens locally with your Ollama models
        </p>
      </div>
    </div>
  );
};
